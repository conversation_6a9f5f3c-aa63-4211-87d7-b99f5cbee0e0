### Business Process Automation API Test Endpoints
### Base URL: http://localhost:5000

### 1. Get all workflows (should return empty initially)
GET http://localhost:5000/api/workflow
Accept: application/json

###

### 2. Create a new workflow with steps
POST http://localhost:5000/api/workflow
Content-Type: application/json

{
  "name": "Leave Request Workflow",
  "description": "Standard workflow for processing leave requests",
  "version": 1,
  "isActive": true,
  "steps": [
    {
      "stepName": "Manager Approval",
      "order": 1,
      "responsibleRole": "Manager",
      "dueInHours": 24
    },
    {
      "stepName": "HR Review",
      "order": 2,
      "responsibleRole": "HR",
      "dueInHours": 48
    },
    {
      "stepName": "Final Approval",
      "order": 3,
      "responsibleRole": "Director",
      "dueInHours": 72
    }
  ]
}

###

### 3. Get active workflows
GET http://localhost:5000/api/workflow/active
Accept: application/json

###

### 4. Get all requests (should return empty initially)
GET http://localhost:5000/api/request
Accept: application/json

###

### 5. Get all notifications (should return empty initially)
GET http://localhost:5000/api/notification
Accept: application/json

###

### 6. Create a test user first (this would normally be done through authentication)
### Note: This endpoint might not exist yet, but shows the intended flow

### 7. Create a new request (requires authentication)
### POST http://localhost:5000/api/request
### Content-Type: application/json
### Authorization: Bearer YOUR_JWT_TOKEN
### 
### {
###   "type": 1,
###   "title": "Annual Leave Request",
###   "description": "Requesting 5 days annual leave from March 1-5, 2024",
###   "workflowId": "WORKFLOW_ID_FROM_STEP_2"
### }

###

### 8. Get workflow with steps (replace {id} with actual workflow ID from step 2)
### GET http://localhost:5000/api/workflow/{id}/steps
### Accept: application/json

###

### 9. Health check - Test if API is running
GET http://localhost:5000/api/weatherforecast
Accept: application/json
